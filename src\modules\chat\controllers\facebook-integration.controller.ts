import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  UseGuards,
  Param,
  Logger,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { ApiResponseDto } from '@/common/response';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { FacebookService } from '../services/facebook.service';
import { FacebookPageConfigRepository } from '../repositories/facebook-page-config.repository';
import { FacebookPageConfig } from '../entities/facebook-page-config.entity';
import { AppException, ErrorCode } from '@/common/exceptions/app.exception';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';

import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUrl } from 'class-validator';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';

/**
 * DTO cho yêu cầu tích hợp Facebook Page
 */
class FacebookPageIntegrationDto {
  /**
   * Mã code từ Facebook OAuth flow
   */
  @ApiProperty({
    description: 'Mã code từ Facebook OAuth flow',
    example: 'AQDUi-i2z7kYt2Vl32cEHk...',
  })
  @IsNotEmpty()
  @IsString()
  code: string;

  /**
   * URL redirect đã đăng ký với Facebook
   */
  @ApiProperty({
    description: 'URL redirect đã đăng ký với Facebook',
    example: 'https://app.yourcompany.com/integrations/facebook/callback',
  })
  @IsNotEmpty()
  @IsUrl()
  redirectUri: string;
}

/**
 * DTO phản hồi cho thông tin Page Facebook
 */
class FacebookPageResponseDto {
  /**
   * ID của Facebook Page
   */
  @ApiProperty({
    description: 'ID của Facebook Page',
    example: '123456789012345',
  })
  pageId: string;

  /**
   * Tên của Facebook Page
   */
  @ApiProperty({
    description: 'Tên của Facebook Page',
    example: 'Your Company Support',
  })
  pageName: string;

  /**
   * Trạng thái đã kích hoạt webhook hay chưa
   */
  @ApiProperty({
    description: 'Trạng thái đã kích hoạt webhook hay chưa',
    example: true,
  })
  webhookEnabled: boolean;

  /**
   * ID của người dùng đã tích hợp Page
   */
  @ApiProperty({
    description: 'ID của người dùng đã tích hợp Page',
    example: 123,
  })
  userId: number;

  /**
   * Đánh dấu nếu Page thuộc về người dùng hiện tại
   */
  @ApiProperty({
    description: 'Đánh dấu nếu Page thuộc về người dùng hiện tại',
    example: true,
  })
  isOwner: boolean;
}

/**
 * Controller xử lý tích hợp Facebook Page
 */
@ApiTags(SWAGGER_API_TAG.CHAT)
@ApiExtraModels(ApiResponseDto, FacebookPageResponseDto)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('/v1/api/chat/facebook-integration')
export class FacebookIntegrationController {
  private readonly logger = new Logger(FacebookIntegrationController.name);

  constructor(
    private readonly facebookService: FacebookService,
    private readonly facebookPageConfigRepository: FacebookPageConfigRepository,
  ) {}

  /**
   * Xử lý mã code từ Facebook OAuth flow và lấy danh sách Page
   */
  @Post('/pages')
  @ApiOperation({
    summary: 'Tích hợp Facebook Page',
    description: 'Xử lý mã code từ Facebook OAuth flow và lấy danh sách Page',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách các Page đã được cấp quyền',
    schema: ApiResponseDto.getSchema(FacebookPageResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi khi gọi dịch vụ Facebook',
  })
  async integratePages(
    @Body() integrationDto: FacebookPageIntegrationDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<FacebookPageResponseDto[]>> {
    try {
      // 1. Đổi code lấy token
      const tokenData = await this.facebookService.exchangeCodeForToken(
        integrationDto.code,
        integrationDto.redirectUri,
      );

      // 2. Lấy danh sách Page được cấp quyền
      const pages = await this.facebookService.getAuthorizedPages(
        tokenData.access_token,
      );

      // 3. Lưu thông tin Page vào database
      const savedPages = await Promise.all(
        pages.map(async (page) => {
          const pageConfig = new FacebookPageConfig();
          pageConfig.pageId = page.id;
          pageConfig.pageName = page.name;
          pageConfig.accessToken = page.access_token;
          pageConfig.tenantId = Number(user.tenantId);
          pageConfig.userId = user.id;
          pageConfig.isActive = true;

          try {
            // Kiểm tra xem page đã tồn tại chưa
            const existingPage =
              await this.facebookPageConfigRepository.findByPageId(page.id);
            if (existingPage) {
              // Cập nhật thông tin
              existingPage.pageName = page.name;
              existingPage.accessToken = page.access_token;
              existingPage.isActive = true;
              await this.facebookPageConfigRepository.savePageConfig(
                existingPage,
              );
              return this.mapToResponseDto(existingPage, user.id);
            } else {
              // Tạo mới
              const savedPage =
                await this.facebookPageConfigRepository.savePageConfig(
                  pageConfig,
                );
              return this.mapToResponseDto(savedPage, user.id);
            }
          } catch (error) {
            this.logger.error(`Error saving page ${page.id}: ${error.message}`);
            throw new AppException(
              ErrorCode.INTERNAL_SERVER_ERROR,
              `Failed to save page configuration: ${error.message}`,
            );
          }
        }),
      );

      return ApiResponseDto.success(savedPages);
    } catch (error) {
      this.logger.error(`Facebook integration error: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Facebook integration failed: ${error.message}`,
      );
    }
  }

  /**
   * Kích hoạt webhook cho Facebook Page
   */
  @Post('/pages/:pageId/webhook')
  @ApiOperation({
    summary: 'Kích hoạt webhook cho Facebook Page',
    description: 'Đăng ký webhook cho Facebook Page',
  })
  @ApiParam({ name: 'pageId', description: 'ID của Facebook Page' })
  @ApiResponse({
    status: 200,
    description: 'Webhook đã được kích hoạt thành công',
    schema: ApiResponseDto.getSchema(FacebookPageResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi khi gọi dịch vụ Facebook',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy Facebook Page',
  })
  async enableWebhook(
    @Param('pageId') pageId: string,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<FacebookPageResponseDto>> {
    try {
      // 1. Kiểm tra xem Page có tồn tại không
      const pageConfig =
        await this.facebookPageConfigRepository.findByPageId(pageId);
      if (!pageConfig) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          `Facebook Page not found: ${pageId}`,
        );
      }

      // 2. Kiểm tra quyền truy cập
      if (pageConfig.tenantId !== Number(user.tenantId)) {
        throw new AppException(
          ErrorCode.FORBIDDEN,
          'You do not have permission to access this page - different tenant',
        );
      }

      // 3. Kiểm tra quyền sở hữu của người dùng
      if (pageConfig.userId !== user.id) {
        throw new AppException(
          ErrorCode.FORBIDDEN,
          'You do not have permission to access this page - not your page',
        );
      }

      // 4. Đăng ký webhook
      await this.facebookService.subscribeAppToPageWebhook(
        pageId,
        pageConfig.accessToken,
      );

      // 5. Cập nhật trạng thái webhook
      const timestamp = Date.now();
      await this.facebookPageConfigRepository.updateWebhookEnabled(
        pageId,
        timestamp,
      );

      // 6. Lấy thông tin Page đã cập nhật
      const updatedPageConfig =
        await this.facebookPageConfigRepository.findByPageId(pageId);

      if (!updatedPageConfig) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          `Facebook Page not found after update: ${pageId}`,
        );
      }

      return ApiResponseDto.success(
        this.mapToResponseDto(updatedPageConfig, user.id),
      );
    } catch (error) {
      this.logger.error(`Enable webhook error: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Failed to enable webhook: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách tất cả Facebook Page đã tích hợp trong công ty
   */
  @Get('/pages')
  @ApiOperation({
    summary: 'Lấy danh sách tất cả Facebook Page đã tích hợp trong công ty',
    description:
      'Lấy danh sách tất cả Facebook Page đã được tích hợp trong công ty',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách Facebook Page',
    schema: ApiResponseDto.getSchema(FacebookPageResponseDto),
  })
  async getPages(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<FacebookPageResponseDto[]>> {
    try {
      const pages = await this.facebookPageConfigRepository.findByTenantId(
        Number(user.tenantId),
      );
      const pageResponses = pages.map((page) =>
        this.mapToResponseDto(page, user.id),
      );

      return ApiResponseDto.success(pageResponses);
    } catch (error) {
      this.logger.error(`Get pages error: ${error.message}`);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Failed to get Facebook pages: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách Facebook Page của người dùng hiện tại
   */
  @Get('/my-pages')
  @ApiOperation({
    summary: 'Lấy danh sách Facebook Page của người dùng hiện tại',
    description:
      'Lấy danh sách Facebook Page đã được tích hợp bởi người dùng hiện tại',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách Facebook Page của người dùng',
    schema: ApiResponseDto.getSchema(FacebookPageResponseDto),
  })
  async getMyPages(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<FacebookPageResponseDto[]>> {
    try {
      const pages = await this.facebookPageConfigRepository.findByUserId(
        user.id,
      );
      const pageResponses = pages.map((page) =>
        this.mapToResponseDto(page, user.id),
      );

      return ApiResponseDto.success(pageResponses);
    } catch (error) {
      this.logger.error(`Get my pages error: ${error.message}`);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Failed to get user's Facebook pages: ${error.message}`,
      );
    }
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param pageConfig Entity FacebookPageConfig
   * @param currentUserId ID của người dùng hiện tại (tùy chọn)
   */
  private mapToResponseDto(
    pageConfig: FacebookPageConfig,
    currentUserId?: number,
  ): FacebookPageResponseDto {
    const userId = pageConfig.userId;
    const isCurrentUserTheOwner = currentUserId
      ? userId === currentUserId
      : false;

    return {
      pageId: pageConfig.pageId,
      pageName: pageConfig.pageName,
      webhookEnabled: !!pageConfig.webhookEnabledAt,
      userId: userId,
      isOwner: isCurrentUserTheOwner,
    };
  }
}
