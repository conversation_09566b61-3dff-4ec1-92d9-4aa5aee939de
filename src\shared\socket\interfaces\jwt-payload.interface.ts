/**
 * Enum định nghĩa các loại token
 */
export enum TokenType {
  ACCESS = 'ACCESS',
  REFRESH = 'REFRESH',
  OTP = 'OTP',
  VERIFY = 'VERIFY',
  TWO_FA = 'TWO_FA',
  CHANGE_PASSWORD = 'CHANGE_PASSWORD',
  FORGOT_PASSWORD = 'FORGOT_PASSWORD',
}

/**
 * Interface định nghĩa cấu trúc của JWT payload
 */
export interface JwtPayload {
  id: number;
  sub: number; // Subject (usually user ID or employee ID)
  username?: string; // Username (optional)
  permissions?: string[]; // User permissions (optional)
  typeToken?: TokenType; // Token type (optional for backward compatibility)
  tenantId?: number | string; // Có thể là chuỗi từ JWT
  domain?: string | null;
  type: 'SYSTEM_ADMIN' | 'COMPANY_ADMIN' | 'EMPLOYEE'; // Loại người dùng
}
