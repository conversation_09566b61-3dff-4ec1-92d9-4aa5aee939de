# Socket.IO Module

Module này cung cấp các tiện ích dùng chung cho Socket.IO để hỗ trợ giao tiếp thời gian thực trong ứng dụng.

## Cài đặt

```bash
npm install @nestjs/websockets @nestjs/platform-socket.io socket.io
```

## Cấu trúc module

```
src/shared/socket/
├── socket.module.ts         # Module chính
├── socket.gateway.ts        # Gateway chính
├── socket.service.ts        # Service chính
├── decorators/             # Các decorator
│   ├── index.ts
│   ├── socket-user.decorator.ts
│   └── socket-room.decorator.ts
├── guards/                 # Các guard
│   ├── index.ts
│   └── socket-auth.guard.ts
├── interfaces/            # Các interface
│   ├── index.ts
│   ├── socket-client.interface.ts
│   └── socket-payload.interface.ts
├── dto/                   # Các DTO
│   ├── index.ts
│   └── socket-message.dto.ts
├── events/                # Các event
│   ├── index.ts
│   └── socket-events.enum.ts
└── README.md              # Tài liệu hướng dẫn
```

## Sử dụng cơ bản

### Khởi tạo module

```typescript
import { Module } from '@nestjs/common';
import { SocketModule } from '@shared/socket/socket.module';

@Module({
  imports: [SocketModule],
})
export class AppModule {}
```

### Gửi tin nhắn từ service

```typescript
import { Injectable } from '@nestjs/common';
import { SocketService } from '@shared/socket/socket.service';

@Injectable()
export class YourService {
  constructor(private readonly socketService: SocketService) {}

  async sendNotification(userId: string, data: any) {
    this.socketService.sendToUser(userId, 'notification', data);
  }
}
```

### Tạo gateway riêng

```typescript
import { WebSocketGateway, SubscribeMessage, MessageBody } from '@nestjs/websockets';
import { SocketAuthGuard } from '@shared/socket/guards/socket-auth.guard';
import { SocketUser } from '@shared/socket/decorators/socket-user.decorator';
import { JwtPayload } from '@modules/auth/interfaces/jwt-payload.interface';

@WebSocketGateway({ namespace: 'your-namespace' })
export class YourGateway {
  @UseGuards(SocketAuthGuard)
  @SubscribeMessage('your-event')
  handleEvent(@MessageBody() data: any, @SocketUser() user: JwtPayload) {
    // Xử lý sự kiện
    return { event: 'your-response', data };
  }
}
```

## Các tính năng chính

- Xác thực người dùng qua JWT token
- Quản lý kết nối và ngắt kết nối
- Quản lý phòng (tạo, tham gia, rời phòng)
- Gửi tin nhắn 1-1, nhóm, broadcast
- Xử lý sự kiện và lỗi

## Các sự kiện chuẩn

- `connection`: Khi client kết nối
- `disconnect`: Khi client ngắt kết nối
- `join_room`: Khi client tham gia phòng
- `leave_room`: Khi client rời phòng
- `message`: Khi client gửi tin nhắn
- `notification`: Khi server gửi thông báo
- `error`: Khi có lỗi xảy ra
