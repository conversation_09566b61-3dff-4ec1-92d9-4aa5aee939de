import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsInt,
  IsOptional,
  IsEnum,
  MaxLength,
  IsEmail,
  MinLength,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';
import { EmployeeStatus } from '../enum/employee-status.enum';

/**
 * DTO cho thông tin tài khoản người dùng
 */
export class UserInfoDto {
  /**
   * Tên đăng nhập
   * @example "user001"
   */
  @ApiProperty({ description: 'Tên đăng nhập', example: 'user001' })
  @IsNotEmpty()
  @IsString()
  @MinLength(4)
  @MaxLength(50)
  username: string;

  /**
   * Mật khẩu
   * @example "Password@123"
   */
  @ApiProperty({ description: 'Mật khẩu', example: 'Password@123' })
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  @MaxLength(50)
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    {
      message:
        'Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt',
    },
  )
  password: string;

  /**
   * Địa chỉ email
   * @example "<EMAIL>"
   */
  @ApiProperty({ description: 'Địa chỉ email', example: '<EMAIL>' })
  @IsNotEmpty()
  @IsEmail()
  @MaxLength(255)
  email: string;

  /**
   * Họ và tên đầy đủ
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({ description: 'Họ và tên đầy đủ', example: 'Nguyễn Văn A' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  fullName: string;
}

/**
 * DTO cho việc tạo tài khoản người dùng cho nhân viên
 */
export class CreateEmployeeWithUserDto {
  /**
   * Thông tin tài khoản người dùng
   */
  @ApiProperty({
    description: 'Thông tin tài khoản người dùng',
    type: () => UserInfoDto,
  })
  @IsNotEmpty()
  userInfo: UserInfoDto;

  /**
   * Mã nhân viên
   * @example "EMP001"
   */
  @ApiProperty({ description: 'Mã nhân viên', example: 'EMP001' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  employeeCode: string;

  /**
   * Tên nhân viên
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({ description: 'Tên nhân viên', example: 'Nguyễn Văn A' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  employeeName: string;

  /**
   * ID phòng ban của nhân viên
   * @example 1
   */
  @ApiProperty({
    required: false,
    description: 'ID phòng ban của nhân viên',
    example: 1,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  departmentId?: number;

  /**
   * Chức danh công việc của nhân viên
   * @example "Kỹ sư phần mềm"
   */
  @ApiProperty({
    required: false,
    description: 'Chức danh công việc của nhân viên',
    example: 'Kỹ sư phần mềm',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  jobTitle?: string;

  /**
   * Trạng thái của nhân viên
   * @example "active"
   */
  @ApiProperty({
    required: false,
    description: 'Trạng thái của nhân viên',
    enum: EmployeeStatus,
    default: EmployeeStatus.ACTIVE,
    example: EmployeeStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EmployeeStatus)
  status?: EmployeeStatus;
}
