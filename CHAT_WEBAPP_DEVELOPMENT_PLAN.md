# Kế Hoạch Phát Triển API Chat WebApp với WebSocket

## 📋 Tổng Quan

### Tình Huống Hiện Tại
- **Module chat hiện tại**: Dành cho Facebook Messenger + AI Assistant (RAG)
- **Bảng database hiện tại**: `chat_messages`, `chat_conversations` (cho Facebook integration)
- **<PERSON>êu cầu mới**: Tạo hệ thống chat cho webapp với WebSocket

### Quyết Định Kiến Trúc
- **Tách riêng module**: Tạo module `webapp-chat` mới, giữ nguyên module `chat` cũ
- **Đổi tên bảng cũ**: Rename để tránh xung đột
- **Sử dụng WebSocket**: Real-time communication cho webapp

## 🗂️ Cấu Trúc Module Mới

```
src/modules/webapp-chat/
├── entities/
│   ├── webapp-chat-room.entity.ts
│   ├── webapp-chat-message.entity.ts
│   └── webapp-chat-participant.entity.ts
├── dto/
│   ├── create-room.dto.ts
│   ├── send-message.dto.ts
│   └── join-room.dto.ts
├── controllers/
│   └── webapp-chat.controller.ts
├── services/
│   ├── webapp-chat.service.ts
│   ├── webapp-chat-room.service.ts
│   └── webapp-chat-websocket.service.ts
├── gateways/
│   └── webapp-chat.gateway.ts
├── repositories/
│   ├── webapp-chat-room.repository.ts
│   ├── webapp-chat-message.repository.ts
│   └── webapp-chat-participant.repository.ts
├── interfaces/
│   └── webapp-chat.interface.ts
├── guards/
│   └── websocket-auth.guard.ts
└── webapp-chat.module.ts
```

## 🔄 Migration Plan - Đổi Tên Bảng Cũ

### Bước 1: Rename Bảng Facebook Chat
```sql
-- Đổi tên bảng cũ để tránh xung đột
ALTER TABLE chat_messages RENAME TO facebook_chat_messages;
ALTER TABLE chat_conversations RENAME TO facebook_chat_conversations;

-- Update indexes
ALTER INDEX idx_chat_messages_user_id RENAME TO idx_facebook_chat_messages_user_id;
ALTER INDEX idx_chat_messages_session_id RENAME TO idx_facebook_chat_messages_session_id;
```

### Bước 2: Update Entity Names
- `ChatMessage` → `FacebookChatMessage`
- `ChatConversation` → `FacebookChatConversation`

## 🗄️ Database Schema - WebApp Chat

### 1. webapp_chat_rooms
```sql
CREATE TABLE webapp_chat_rooms (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    room_type VARCHAR(50) NOT NULL DEFAULT 'group', -- 'direct', 'group', 'channel'
    is_private BOOLEAN DEFAULT false,
    max_participants INTEGER DEFAULT 100,
    created_by INTEGER NOT NULL,
    avatar_url VARCHAR(500),
    metadata JSONB,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    tenant_id INTEGER NOT NULL,
    
    CONSTRAINT fk_webapp_chat_rooms_creator FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_webapp_chat_rooms_tenant FOREIGN KEY (tenant_id) REFERENCES company_accounts(id)
);

CREATE INDEX idx_webapp_chat_rooms_tenant_id ON webapp_chat_rooms(tenant_id);
CREATE INDEX idx_webapp_chat_rooms_type ON webapp_chat_rooms(room_type);
CREATE INDEX idx_webapp_chat_rooms_created_by ON webapp_chat_rooms(created_by);
```

### 2. webapp_chat_participants
```sql
CREATE TABLE webapp_chat_participants (
    id SERIAL PRIMARY KEY,
    room_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    role VARCHAR(50) DEFAULT 'member', -- 'admin', 'moderator', 'member'
    joined_at BIGINT NOT NULL,
    last_read_at BIGINT,
    is_muted BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    tenant_id INTEGER NOT NULL,
    
    CONSTRAINT fk_webapp_chat_participants_room FOREIGN KEY (room_id) REFERENCES webapp_chat_rooms(id) ON DELETE CASCADE,
    CONSTRAINT fk_webapp_chat_participants_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_webapp_chat_participants_tenant FOREIGN KEY (tenant_id) REFERENCES company_accounts(id),
    UNIQUE(room_id, user_id)
);

CREATE INDEX idx_webapp_chat_participants_room_id ON webapp_chat_participants(room_id);
CREATE INDEX idx_webapp_chat_participants_user_id ON webapp_chat_participants(user_id);
CREATE INDEX idx_webapp_chat_participants_tenant_id ON webapp_chat_participants(tenant_id);
```

### 3. webapp_chat_messages
```sql
CREATE TABLE webapp_chat_messages (
    id SERIAL PRIMARY KEY,
    room_id INTEGER NOT NULL,
    sender_id INTEGER NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text', -- 'text', 'image', 'file', 'system'
    content TEXT,
    attachments JSONB,
    reply_to_id INTEGER,
    is_edited BOOLEAN DEFAULT false,
    is_deleted BOOLEAN DEFAULT false,
    metadata JSONB,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    tenant_id INTEGER NOT NULL,
    
    CONSTRAINT fk_webapp_chat_messages_room FOREIGN KEY (room_id) REFERENCES webapp_chat_rooms(id) ON DELETE CASCADE,
    CONSTRAINT fk_webapp_chat_messages_sender FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT fk_webapp_chat_messages_reply FOREIGN KEY (reply_to_id) REFERENCES webapp_chat_messages(id) ON DELETE SET NULL,
    CONSTRAINT fk_webapp_chat_messages_tenant FOREIGN KEY (tenant_id) REFERENCES company_accounts(id)
);

CREATE INDEX idx_webapp_chat_messages_room_id ON webapp_chat_messages(room_id);
CREATE INDEX idx_webapp_chat_messages_sender_id ON webapp_chat_messages(sender_id);
CREATE INDEX idx_webapp_chat_messages_created_at ON webapp_chat_messages(created_at);
CREATE INDEX idx_webapp_chat_messages_tenant_id ON webapp_chat_messages(tenant_id);
```

## 🔌 WebSocket Implementation

### 1. WebSocket Gateway
```typescript
@WebSocketGateway({
  namespace: '/webapp-chat',
  cors: { origin: '*' }
})
export class WebAppChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  
  @SubscribeMessage('join-room')
  async handleJoinRoom(client: Socket, payload: JoinRoomDto) {
    // Join room logic
  }
  
  @SubscribeMessage('send-message')
  async handleSendMessage(client: Socket, payload: SendMessageDto) {
    // Send message logic
  }
  
  @SubscribeMessage('typing-start')
  async handleTypingStart(client: Socket, payload: TypingDto) {
    // Typing indicator
  }
}
```

### 2. WebSocket Events
```typescript
// Client → Server Events
interface ClientToServerEvents {
  'join-room': (data: JoinRoomDto) => void;
  'leave-room': (data: LeaveRoomDto) => void;
  'send-message': (data: SendMessageDto) => void;
  'typing-start': (data: TypingDto) => void;
  'typing-stop': (data: TypingDto) => void;
  'mark-as-read': (data: MarkAsReadDto) => void;
}

// Server → Client Events
interface ServerToClientEvents {
  'message-received': (data: MessageReceivedEvent) => void;
  'user-joined': (data: UserJoinedEvent) => void;
  'user-left': (data: UserLeftEvent) => void;
  'typing-indicator': (data: TypingIndicatorEvent) => void;
  'room-updated': (data: RoomUpdatedEvent) => void;
  'error': (data: ErrorEvent) => void;
}
```

## 🛠️ Implementation Phases

### Phase 1: Core Infrastructure (Week 1)
1. **Migration & Rename**
   - [ ] Tạo migration đổi tên bảng cũ
   - [ ] Update entity names trong module chat cũ
   - [ ] Test Facebook chat vẫn hoạt động

2. **Basic Entities & DTOs**
   - [ ] Tạo webapp-chat entities
   - [ ] Tạo basic DTOs
   - [ ] Setup module structure

### Phase 2: WebSocket Foundation (Week 2)
1. **WebSocket Gateway**
   - [ ] Implement WebAppChatGateway
   - [ ] Connection/disconnection handling
   - [ ] Room management
   - [ ] Authentication guard

2. **Core Services**
   - [ ] WebAppChatService
   - [ ] WebAppChatRoomService
   - [ ] Repository implementations

### Phase 3: Chat Features (Week 3)
1. **Messaging**
   - [ ] Send/receive messages
   - [ ] Message types (text, file, image)
   - [ ] Reply to messages
   - [ ] Edit/delete messages

2. **Room Management**
   - [ ] Create/join/leave rooms
   - [ ] Room permissions
   - [ ] Participant management

### Phase 4: Advanced Features (Week 4)
1. **Real-time Features**
   - [ ] Typing indicators
   - [ ] Read receipts
   - [ ] Online status
   - [ ] Message notifications

2. **File Handling**
   - [ ] File upload integration
   - [ ] Image preview
   - [ ] File download

### Phase 5: API & Integration (Week 5)
1. **REST APIs**
   - [ ] Room CRUD operations
   - [ ] Message history
   - [ ] Search functionality
   - [ ] User management

2. **Frontend Integration**
   - [ ] WebSocket client setup
   - [ ] Chat UI components
   - [ ] Real-time updates

## 🔐 Security & Authentication

### WebSocket Authentication
```typescript
@Injectable()
export class WebSocketAuthGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const client = context.switchToWs().getClient<Socket>();
    const token = client.handshake.auth.token;
    // Validate JWT token
    return this.validateToken(token);
  }
}
```

### Tenant Isolation
- Tất cả operations phải include `tenantId`
- WebSocket rooms isolated by tenant
- Message access control by tenant

## 📊 Performance Considerations

### Database Optimization
- Indexes on frequently queried fields
- Pagination for message history
- Message archiving strategy

### WebSocket Optimization
- Connection pooling
- Room-based message broadcasting
- Memory management for active connections

### Caching Strategy
- Redis for active room participants
- Cache recent messages
- Online user status caching

## 🧪 Testing Strategy

### Unit Tests
- Service layer testing
- Repository testing
- DTO validation

### Integration Tests
- WebSocket connection testing
- Database operations
- Authentication flow

### E2E Tests
- Complete chat flow
- Multi-user scenarios
- Real-time features

## 📝 API Documentation

### REST Endpoints
```
GET    /api/webapp-chat/rooms              # Get user's rooms
POST   /api/webapp-chat/rooms              # Create room
GET    /api/webapp-chat/rooms/:id/messages # Get room messages
POST   /api/webapp-chat/rooms/:id/join     # Join room
DELETE /api/webapp-chat/rooms/:id/leave    # Leave room
```

### WebSocket Events Documentation
- Event schemas
- Error handling
- Connection lifecycle

## 🚀 Deployment Considerations

### Environment Variables
```env
WEBAPP_CHAT_REDIS_URL=redis://localhost:6379
WEBAPP_CHAT_MAX_CONNECTIONS=1000
WEBAPP_CHAT_MESSAGE_RETENTION_DAYS=365
```

### Monitoring
- WebSocket connection metrics
- Message throughput
- Error rates
- Performance monitoring

---

## 📋 Next Steps

1. **Immediate**: Tạo migration đổi tên bảng cũ
2. **Week 1**: Implement core entities và module structure
3. **Week 2**: WebSocket gateway và basic messaging
4. **Week 3-5**: Feature development theo phases

**Estimated Timeline**: 5 weeks
**Priority**: High (Core business feature)
**Dependencies**: Redis, WebSocket support, File upload service
