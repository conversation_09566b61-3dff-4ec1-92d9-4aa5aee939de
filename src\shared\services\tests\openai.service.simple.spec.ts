// Define interfaces for testing
interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string | null;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Mock OpenAI service
class OpenAiService {
  async chatCompletion(
    messages: any[],
    model: string = 'gpt-3.5-turbo',
  ): Promise<ChatCompletionResponse> {
    return {
      id: 'test-id',
      object: 'chat.completion',
      created: Date.now(),
      model,
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content: 'Hello! How can I help you today?',
          },
          finish_reason: 'stop',
        },
      ],
      usage: {
        prompt_tokens: 10,
        completion_tokens: 10,
        total_tokens: 20,
      },
    };
  }

  async simpleChatCompletion(
    message: string,
    systemPrompt: string = 'Bạn là trợ lý AI hữu ích.',
    model: string = 'gpt-3.5-turbo',
  ): Promise<string> {
    const response = await this.chatCompletion(
      [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: message },
      ],
      model,
    );
    return (
      response.choices[0]?.message?.content || 'Không thể tạo câu trả lời.'
    );
  }
}

describe('OpenAiService', () => {
  let service: OpenAiService;

  beforeEach(() => {
    service = new OpenAiService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('simpleChatCompletion', () => {
    it('should return the content of the first message choice', async () => {
      // Arrange
      const message = 'Hello!';
      const systemPrompt = 'You are a helpful assistant.';
      const model = 'gpt-3.5-turbo';
      const expectedContent = 'Hello! How can I help you today?';

      // Mock chatCompletion to return a response with the expected content
      jest.spyOn(service, 'chatCompletion').mockResolvedValueOnce({
        id: 'test-id',
        object: 'chat.completion',
        created: Date.now(),
        model,
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: expectedContent,
            },
            finish_reason: 'stop',
          },
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 10,
          total_tokens: 20,
        },
      });

      // Act
      const result = await service.simpleChatCompletion(
        message,
        systemPrompt,
        model,
      );

      // Assert
      expect(service.chatCompletion).toHaveBeenCalledWith(
        [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: message },
        ],
        model,
      );
      expect(result).toBe(expectedContent);
    });

    it('should return default message when no content is available', async () => {
      // Arrange
      const message = 'Hello!';

      // Mock chatCompletion to return a response with no content
      jest.spyOn(service, 'chatCompletion').mockResolvedValueOnce({
        id: 'test-id',
        object: 'chat.completion',
        created: Date.now(),
        model: 'gpt-3.5-turbo',
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: null,
            },
            finish_reason: 'stop',
          },
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 0,
          total_tokens: 10,
        },
      });

      // Act
      const result = await service.simpleChatCompletion(message);

      // Assert
      expect(result).toBe('Không thể tạo câu trả lời.');
    });

    it('should throw error when chatCompletion throws an error', async () => {
      // Arrange
      const message = 'Hello!';
      const errorMessage = 'API error';

      // Mock chatCompletion to throw an error
      jest
        .spyOn(service, 'chatCompletion')
        .mockRejectedValueOnce(new Error(errorMessage));

      // Act & Assert
      await expect(service.simpleChatCompletion(message)).rejects.toThrow(
        errorMessage,
      );
    });
  });
});
