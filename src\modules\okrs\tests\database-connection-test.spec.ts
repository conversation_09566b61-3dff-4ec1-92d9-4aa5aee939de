import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { OkrCycle } from '../entities/okr-cycle.entity';

/**
 * Test kết nối database với entity OkrCycle
 */
describe('Database Connection Test', () => {
  let module: TestingModule;
  let dataSource: DataSource;

  beforeAll(async () => {
    // Tạo testing module với TypeOrmModule
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: 'pub-ai-erp-yzkr13ma-1099002.dbaas.bfcplatform.vn',
          port: 5432,
          username: 'root',
          password: 'dFmTCcoF8xZn0H21uBrZZrWZ0xI5OULiUA8i',
          database: 'postgres',
          entities: [OkrCycle],
          synchronize: false,
          ssl: {
            rejectUnauthorized: false,
          },
        }),
      ],
    }).compile();

    // Lấy DataSource
    dataSource = module.get<DataSource>(DataSource);
  });

  afterAll(async () => {
    // Đóng kết nối sau khi test xong
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    await module.close();
  });

  it('should connect to the database successfully', async () => {
    // Kiểm tra kết nối đã được thiết lập
    expect(dataSource.isInitialized).toBe(true);

    // Thử thực hiện một truy vấn đơn giản
    const result = await dataSource.query('SELECT 1 as value');
    expect(result).toBeDefined();
    expect(result[0].value).toBe(1);
  });

  it('should have OkrCycle entity registered', () => {
    // Kiểm tra entity OkrCycle đã được đăng ký
    const entities = dataSource.entityMetadatas;
    const okrCycleEntity = entities.find(
      (entity) => entity.name === 'OkrCycle',
    );

    expect(okrCycleEntity).toBeDefined();
    expect(okrCycleEntity.name).toBe('OkrCycle');
    expect(okrCycleEntity.tableName).toBe('okr_cycles');
  });
});
