import { Test, TestingModule } from '@nestjs/testing';
import { EncryptionService } from '../encryption.service';
import { ConfigService } from '@nestjs/config';

describe('EncryptionService', () => {
  let service: EncryptionService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EncryptionService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === 'ENCRYPTION_SECRET_KEY') {
                return 'test-secret-key-for-encryption-service-unit-tests';
              }
              return null;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<EncryptionService>(EncryptionService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('encrypt and decrypt', () => {
    it('should encrypt and decrypt text correctly', () => {
      const originalText = 'This is a secret message';
      const encrypted = service.encrypt(originalText);

      // Kiểm tra chuỗi đã mã hóa khác với chuỗi gốc
      expect(encrypted).not.toEqual(originalText);

      // Giải mã và kiểm tra kết quả
      const decrypted = service.decrypt(encrypted);
      expect(decrypted).toEqual(originalText);
    });

    it('should handle empty strings', () => {
      const originalText = '';
      const encrypted = service.encrypt(originalText);
      const decrypted = service.decrypt(encrypted);
      expect(decrypted).toEqual(originalText);
    });

    it('should handle special characters', () => {
      const originalText = '!@#$%^&*()_+{}|:"<>?[];\',./-=';
      const encrypted = service.encrypt(originalText);
      const decrypted = service.decrypt(encrypted);
      expect(decrypted).toEqual(originalText);
    });

    it('should handle Unicode characters', () => {
      const originalText = 'Xin chào thế giới! 你好世界! こんにちは世界!';
      const encrypted = service.encrypt(originalText);
      const decrypted = service.decrypt(encrypted);
      expect(decrypted).toEqual(originalText);
    });
  });

  describe('encryptObject and decryptObject', () => {
    it('should encrypt and decrypt objects correctly', () => {
      const originalObject = {
        id: 123,
        name: 'John Doe',
        email: '<EMAIL>',
        roles: ['admin', 'user'],
        metadata: {
          lastLogin: '2023-01-01',
          preferences: {
            theme: 'dark',
            notifications: true,
          },
        },
      };

      const encrypted = service.encryptObject(originalObject);

      // Kiểm tra chuỗi đã mã hóa không phải là JSON
      expect(() => JSON.parse(encrypted)).toThrow();

      // Giải mã và kiểm tra kết quả
      const decrypted = service.decryptObject(encrypted);
      expect(decrypted).toEqual(originalObject);
    });
  });

  describe('password hashing and verification', () => {
    it('should hash passwords and verify them correctly', () => {
      const password = 'StrongP@ssw0rd!';
      const hashedPassword = service.hashPassword(password);

      // Kiểm tra chuỗi đã hash khác với mật khẩu gốc
      expect(hashedPassword).not.toEqual(password);

      // Xác thực mật khẩu
      const isValid = service.verifyPassword(password, hashedPassword);
      expect(isValid).toBe(true);

      // Xác thực mật khẩu sai
      const isInvalid = service.verifyPassword('WrongPassword', hashedPassword);
      expect(isInvalid).toBe(false);
    });
  });

  describe('generateRandomToken', () => {
    it('should generate random tokens of specified length', () => {
      const token1 = service.generateRandomToken();
      const token2 = service.generateRandomToken();

      // Kiểm tra hai token khác nhau
      expect(token1).not.toEqual(token2);

      // Kiểm tra độ dài mặc định (32 bytes = 64 ký tự hex)
      expect(token1.length).toBe(64);

      // Kiểm tra độ dài tùy chỉnh
      const customToken = service.generateRandomToken(16);
      expect(customToken.length).toBe(32); // 16 bytes = 32 ký tự hex
    });
  });
});
