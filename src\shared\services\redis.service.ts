import {
  Injectable,
  Logger,
  OnM<PERSON>ule<PERSON><PERSON>roy,
  OnModuleInit,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis, { Redis as RedisClient } from 'ioredis'; // Import Redis và type RedisClient
import { AppException, ErrorCode } from '@/common';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private client: RedisClient;

  constructor(private readonly configService: ConfigService) {}

  onModuleInit() {
    const redisUrl = this.configService.get<string>('REDIS_URL');
    if (!redisUrl) {
      throw new AppException(
        ErrorCode.REDIS_ERROR,
        'REDIS_URL environment variable is not defined.',
      );
    }

    try {
      // Kết nối với URL mới không cần TLS
      this.client = new Redis(redisUrl, {
        // Bỏ cấu hình tls vì dùng redis://
        // tls: {
        //     rejectUnauthorized: false,
        // },
        // <PERSON><PERSON><PERSON> tùy chọn khác có thể thêm vào đây
        // lazyConnect: true,
        // maxRetriesPerRequest: 3,
      });

      this.client.on('connect', () => {
        this.logger.log('Connected to Redis successfully.');
      });

      this.client.on('error', (error) => {
        this.logger.error('Redis connection error:', error);
        // Có thể thêm logic xử lý lỗi kết nối ở đây, ví dụ thử kết nối lại hoặc báo động
      });
    } catch (error) {
      this.logger.error('Failed to initialize Redis client:', error);
      throw new AppException(
        ErrorCode.REDIS_ERROR,
        'Failed to initialize Redis client',
        error,
      );
    }
  }

  async onModuleDestroy() {
    if (this.client) {
      await this.client.quit();
      this.logger.log('Redis connection closed.');
    }
  }

  getClient(): RedisClient {
    if (!this.client) {
      // Điều này không nên xảy ra nếu onModuleInit thành công, nhưng để đề phòng
      throw new AppException(
        ErrorCode.REDIS_ERROR,
        'Redis client is not initialized.',
      );
    }
    return this.client;
  }

  /**
   * Lấy giá trị từ Redis.
   * @param key Khóa cần lấy.
   * @returns Promise<string | null> Giá trị dạng string hoặc null nếu không tìm thấy.
   */
  async get(key: string): Promise<string | null> {
    try {
      return await this.getClient().get(key);
    } catch (error) {
      this.logger.error(`Error getting key ${key} from Redis:`, error);
      throw new AppException(
        ErrorCode.REDIS_ERROR,
        `Error getting key ${key} from Redis`,
        error,
      );
    }
  }

  /**
   * Thiết lập giá trị trong Redis.
   * @param key Khóa cần đặt.
   * @param value Giá trị (sẽ được chuyển thành string).
   * @returns Promise<'OK'>
   */
  async set(key: string, value: string | number | Buffer): Promise<'OK'> {
    try {
      return await this.getClient().set(key, value);
    } catch (error) {
      this.logger.error(`Error setting key ${key} in Redis:`, error);
      throw new AppException(
        ErrorCode.REDIS_ERROR,
        `Error setting key ${key} in Redis`,
        error,
      );
    }
  }

  /**
   * Thiết lập giá trị trong Redis với thời gian hết hạn.
   * @param key Khóa cần đặt.
   * @param value Giá trị.
   * @param expiryInSeconds Thời gian hết hạn tính bằng giây.
   * @returns Promise<'OK'>
   */
  async setWithExpiry(
    key: string,
    value: string | number | Buffer,
    expiryInSeconds: number,
  ): Promise<'OK'> {
    if (expiryInSeconds <= 0) {
      this.logger.error('Expiry time must be positive for setWithExpiry.');
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Expiry time must be positive.',
      );
    }
    try {
      return await this.getClient().setex(key, expiryInSeconds, value);
    } catch (error) {
      this.logger.error(
        `Error setting key ${key} with expiry in Redis:`,
        error,
      );
      throw new AppException(
        ErrorCode.REDIS_ERROR,
        `Error setting key ${key} with expiry in Redis`,
        error,
      );
    }
  }

  /**
   * Xóa một hoặc nhiều khóa khỏi Redis.
   * @param keys Khóa hoặc danh sách các khóa cần xóa.
   * @returns Promise<number> Số lượng khóa đã bị xóa.
   */
  async del(keys: string | string[]): Promise<number> {
    try {
      let result: number;
      if (Array.isArray(keys)) {
        // Nếu là mảng, đảm bảo không rỗng và dùng spread operator
        if (keys.length === 0) {
          return 0;
        }
        result = await this.getClient().del(...keys);
      } else {
        // Nếu là một chuỗi đơn
        result = await this.getClient().del(keys);
      }
      return result;
    } catch (error) {
      this.logger.error(
        `Error deleting keys ${JSON.stringify(keys)} from Redis:`,
        error,
      );
      throw new AppException(
        ErrorCode.REDIS_ERROR,
        `Error deleting keys from Redis`,
        error,
      );
    }
  }

  /**
   * Kiểm tra xem một khóa có tồn tại không.
   * @param key Khóa cần kiểm tra.
   * @returns Promise<boolean> True nếu khóa tồn tại, false nếu không.
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.getClient().exists(key);
      return result === 1;
    } catch (error) {
      this.logger.error(
        `Error checking existence of key ${key} in Redis:`,
        error,
      );
      throw new AppException(
        ErrorCode.REDIS_ERROR,
        `Error checking existence of key in Redis`,
        error,
      );
    }
  }

  /**
   * Tăng giá trị của một khóa lên 1 (nếu là số).
   * @param key Khóa cần tăng.
   * @returns Promise<number> Giá trị mới của khóa sau khi tăng.
   */
  async incr(key: string): Promise<number> {
    try {
      return await this.getClient().incr(key);
    } catch (error) {
      this.logger.error(`Error incrementing key ${key} in Redis:`, error);
      throw new AppException(
        ErrorCode.REDIS_ERROR,
        `Error incrementing key in Redis`,
        error,
      );
    }
  }

  /**
   * Giảm giá trị của một khóa xuống 1 (nếu là số).
   * @param key Khóa cần giảm.
   * @returns Promise<number> Giá trị mới của khóa sau khi giảm.
   */
  async decr(key: string): Promise<number> {
    try {
      return await this.getClient().decr(key);
    } catch (error) {
      this.logger.error(`Error decrementing key ${key} in Redis:`, error);
      throw new AppException(
        ErrorCode.REDIS_ERROR,
        `Error decrementing key in Redis`,
        error,
      );
    }
  }
}
