import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import {
  ConfigModule,
  ConfigService,
  ConfigType,
  DatabaseConfig,
} from '@/config';
import { OkrCycle } from '../entities/okr-cycle.entity';
import { Logger } from '@nestjs/common';
import { OkrCycleStatus } from '../enum/okr-cycle-status.enum';
import { OkrCycleRepository } from '../repositories/okr-cycle.repository';
import { OkrCycleService } from '../services/okr-cycle.service';
import { CreateOkrCycleDto, OkrCycleQueryDto } from '../dto/okr-cycle';
import { TenantContext } from '@/common/context/tenant.context';

/**
 * Test OkrCycleService với kết nối database thực
 */
describe('OkrCycleService', () => {
  let module: TestingModule;
  let dataSource: DataSource;
  let service: OkrCycleService;
  let repository: OkrCycleRepository;
  const logger = new Logger('OkrCycleServiceTest');
  let testCycleId: number;
  const testTenantId = 999;

  beforeAll(async () => {
    // Tạo testing module với TypeOrmModule
    module = await Test.createTestingModule({
      imports: [
        // Import ConfigModule để lấy cấu hình database
        ConfigModule,
        // Import TypeOrmModule với cấu hình từ ConfigService
        TypeOrmModule.forRootAsync({
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => {
            const dbConfig = configService.getConfig<DatabaseConfig>(
              ConfigType.Database,
            );
            logger.log(
              `Connecting to database: ${dbConfig.database} on ${dbConfig.host}:${dbConfig.port}`,
            );

            return {
              type: 'postgres',
              host: dbConfig.host,
              port: dbConfig.port,
              username: dbConfig.username,
              password: dbConfig.password,
              database: dbConfig.database,
              entities: [OkrCycle],
              synchronize: false,
              ssl: {
                rejectUnauthorized: !dbConfig.ssl,
              },
            };
          },
        }),
        // Import TypeOrmModule.forFeature để sử dụng repository
        TypeOrmModule.forFeature([OkrCycle]),
      ],
      providers: [OkrCycleRepository, OkrCycleService],
    }).compile();

    // Lấy DataSource, Service và Repository
    dataSource = module.get<DataSource>(DataSource);
    service = module.get<OkrCycleService>(OkrCycleService);
    repository = module.get<OkrCycleRepository>(OkrCycleRepository);
  });

  afterAll(async () => {
    // Xóa dữ liệu test
    await cleanupTestData();

    // Đóng kết nối sau khi test xong
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    await module.close();
  });

  /**
   * Xóa dữ liệu test
   */
  async function cleanupTestData() {
    try {
      if (testCycleId) {
        // Thiết lập tenant context cho việc xóa
        TenantContext.setCurrentTenantId(testTenantId);
        await repository.delete(testCycleId);
        logger.log(`Deleted test OkrCycle with ID: ${testCycleId}`);
        TenantContext.clear();
      }
    } catch (error) {
      logger.error(`Failed to cleanup test data: ${error.message}`);
    }
  }

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create a new OKR cycle', async () => {
    // Thiết lập tenant context
    TenantContext.setCurrentTenantId(testTenantId);

    // Tạo DTO
    const createDto: CreateOkrCycleDto = {
      name: 'Test Service Cycle',
      startDate: '2025-01-01',
      endDate: '2025-03-31',
      status: OkrCycleStatus.PLANNING,
    };

    // Tạo chu kỳ mới
    const userId = 1; // User ID giả lập
    const result = await service.create(userId, createDto);

    expect(result).toBeDefined();
    expect(result.name).toBe(createDto.name);
    expect(result.status).toBe(createDto.status);

    // Lưu ID để xóa sau
    testCycleId = result.id;

    // Xóa tenant context
    TenantContext.clear();
  });

  it('should find a cycle by ID', async () => {
    // Thiết lập tenant context
    TenantContext.setCurrentTenantId(testTenantId);

    // Tìm chu kỳ theo ID
    const result = await service.findById(testTenantId, testCycleId);

    expect(result).toBeDefined();
    expect(result.id).toBe(testCycleId);
    expect(result.name).toBe('Test Service Cycle');

    // Xóa tenant context
    TenantContext.clear();
  });

  it('should find all cycles with pagination', async () => {
    // Thiết lập tenant context
    TenantContext.setCurrentTenantId(testTenantId);

    // Tìm tất cả chu kỳ với phân trang
    const query = new OkrCycleQueryDto();
    query.page = 1;
    query.limit = 10;

    const result = await service.findAll(testTenantId, query);

    expect(result).toBeDefined();
    expect(result.items).toBeDefined();
    expect(result.meta).toBeDefined();
    expect(result.meta.totalItems).toBeGreaterThanOrEqual(1);

    // Xóa tenant context
    TenantContext.clear();
  });

  it('should update a cycle', async () => {
    // Thiết lập tenant context
    TenantContext.setCurrentTenantId(testTenantId);

    // Cập nhật chu kỳ
    const updateDto = {
      name: 'Updated Service Cycle',
      status: OkrCycleStatus.ACTIVE,
    };

    const result = await service.update(testTenantId, testCycleId, updateDto);

    expect(result).toBeDefined();
    expect(result.id).toBe(testCycleId);
    expect(result.name).toBe(updateDto.name);
    expect(result.status).toBe(updateDto.status);

    // Xóa tenant context
    TenantContext.clear();
  });

  it('should find active cycle', async () => {
    // Thiết lập tenant context
    TenantContext.setCurrentTenantId(testTenantId);

    // Tìm chu kỳ active
    const result = await service.findActive(testTenantId);

    expect(result).toBeDefined();
    expect(result.status).toBe(OkrCycleStatus.ACTIVE);

    // Xóa tenant context
    TenantContext.clear();
  });

  it('should delete a cycle', async () => {
    // Thiết lập tenant context
    TenantContext.setCurrentTenantId(testTenantId);

    // Xóa chu kỳ
    const result = await service.delete(testTenantId, testCycleId);

    expect(result).toBe(true);

    // Đánh dấu là đã xóa để không cần xóa lại trong afterAll
    testCycleId = null;

    // Xóa tenant context
    TenantContext.clear();
  });
});
