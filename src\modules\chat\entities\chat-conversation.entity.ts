import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

/**
 * Entity đại diện cho cuộc hội thoại chat
 */
@Entity('chat_conversations')
@Index(['facebookUserId', 'pageId'])
@Index(['tenantId', 'status'])
export class ChatConversation {
  /**
   * Unique identifier for the conversation
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Facebook User ID của người gửi tin nhắn
   */
  @Column({
    name: 'facebook_user_id',
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  facebookUserId: string;

  /**
   * Facebook Page ID mà cuộc hội thoại diễn ra
   */
  @Column({ name: 'page_id', type: 'varchar', length: 100, nullable: false })
  pageId: string;

  /**
   * Tên hiển thị của người dùng Facebook
   */
  @Column({ name: 'user_name', type: 'varchar', length: 255, nullable: true })
  userName: string | null;

  /**
   * Avatar URL của người dùng Facebook
   */
  @Column({ name: 'user_avatar', type: 'varchar', length: 500, nullable: true })
  userAvatar: string | null;

  /**
   * Trạng thái cuộc hội thoại: active, closed, waiting, transferred
   */
  @Column({ type: 'varchar', length: 50, default: 'active' })
  status: string;

  /**
   * ID của nhân viên được assign để xử lý (nếu có)
   */
  @Column({ name: 'assigned_agent_id', type: 'integer', nullable: true })
  assignedAgentId: number | null;

  /**
   * Loại cuộc hội thoại: auto (AI), manual (human), mixed
   */
  @Column({
    name: 'conversation_type',
    type: 'varchar',
    length: 50,
    default: 'auto',
  })
  conversationType: string;

  /**
   * Ngôn ngữ của cuộc hội thoại
   */
  @Column({ type: 'varchar', length: 10, default: 'vi' })
  language: string;

  /**
   * Metadata bổ sung (JSON)
   */
  @Column({ type: 'jsonb', nullable: true })
  metadata: any;

  /**
   * Thời gian tin nhắn cuối cùng
   */
  @Column({ name: 'last_message_at', type: 'bigint', nullable: true })
  lastMessageAt: number | null;

  /**
   * Thời gian tạo cuộc hội thoại
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Thời gian cập nhật cuối
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID của tenant/company
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: false })
  tenantId: number;
}
