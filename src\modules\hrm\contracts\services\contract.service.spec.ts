import { Test, TestingModule } from '@nestjs/testing';
import { ContractService } from './contract.service';
import { ContractRepository } from '../repositories/contract.repository';
import { Contract } from '../entities/contract.entity';
import { CreateContractDto } from '../dto/create-contract.dto';
import { UpdateContractDto } from '../dto/update-contract.dto';
import { UpdateContractStatusDto } from '../dto/update-contract-status.dto';
import { TerminateContractDto } from '../dto/terminate-contract.dto';
import { ContractStatus } from '../enum/contract-status.enum';
import { ContractType } from '../enum/contract-type.enum';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';

describe('ContractService', () => {
  let service: ContractService;
  let repository: ContractRepository;

  const mockContractRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    findByContractCode: jest.fn(),
    findByEmployeeId: jest.fn(),
    findActiveContractByEmployeeId: jest.fn(),
    findContractsExpiringSoon: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContractService,
        {
          provide: ContractRepository,
          useValue: mockContractRepository,
        },
      ],
    }).compile();

    service = module.get<ContractService>(ContractService);
    repository = module.get<ContractRepository>(ContractRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated contracts with tenantId', async () => {
      const tenantId = 1;
      const mockQuery = { page: 1, limit: 10 };
      const mockResult = {
        items: [{ id: 1, contractCode: 'CT-2023-001', tenantId }] as Contract[],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      mockContractRepository.findAll.mockResolvedValue(mockResult);

      const result = await service.findAll(tenantId, mockQuery);

      expect(result).toEqual(mockResult);
      expect(mockContractRepository.findAll).toHaveBeenCalledWith(
        tenantId,
        mockQuery,
      );
    });

    it('should throw an exception when repository throws an error', async () => {
      const tenantId = 1;
      const mockQuery = { page: 1, limit: 10 };
      mockContractRepository.findAll.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(service.findAll(tenantId, mockQuery)).rejects.toThrow(
        AppException,
      );
      expect(mockContractRepository.findAll).toHaveBeenCalledWith(
        tenantId,
        mockQuery,
      );
    });
  });

  describe('findById', () => {
    it('should return a contract when found with tenantId', async () => {
      const tenantId = 1;
      const contractId = 1;
      const mockContract = {
        id: contractId,
        contractCode: 'CT-2023-001',
        tenantId,
      } as Contract;
      mockContractRepository.findById.mockResolvedValue(mockContract);

      const result = await service.findById(tenantId, contractId);

      expect(result).toEqual(mockContract);
      expect(mockContractRepository.findById).toHaveBeenCalledWith(
        tenantId,
        contractId,
      );
    });

    it('should throw an exception when contract not found', async () => {
      const tenantId = 1;
      const contractId = 1;
      mockContractRepository.findById.mockResolvedValue(null);

      await expect(service.findById(tenantId, contractId)).rejects.toThrow(
        new AppException(
          HRM_ERROR_CODES.CONTRACT_NOT_FOUND,
          'Contract with ID 1 not found',
        ),
      );
      expect(mockContractRepository.findById).toHaveBeenCalledWith(
        tenantId,
        contractId,
      );
    });
  });

  describe('create', () => {
    it('should create and return a new contract with tenantId', async () => {
      const tenantId = 1;
      const userId = 1;
      const mockCreateDto: CreateContractDto = {
        contractCode: 'CT-2023-001',
        employeeId: 1,
        contractType: ContractType.DEFINITE,
        title: 'Employment Contract',
        startDate: new Date('2023-01-15'),
        baseSalary: 10000000,
      };
      const mockCreatedContract = {
        id: 1,
        contractCode: 'CT-2023-001',
        employeeId: 1,
        contractType: ContractType.DEFINITE,
        title: 'Employment Contract',
        startDate: new Date('2023-01-15'),
        baseSalary: 10000000,
        status: ContractStatus.DRAFT,
        tenantId,
      } as Contract;

      mockContractRepository.findByContractCode.mockResolvedValue(null);
      mockContractRepository.create.mockResolvedValue(mockCreatedContract);

      const result = await service.create(tenantId, mockCreateDto, userId);

      expect(result).toEqual(mockCreatedContract);
      expect(mockContractRepository.findByContractCode).toHaveBeenCalledWith(
        tenantId,
        'CT-2023-001',
      );
      expect(mockContractRepository.create).toHaveBeenCalledWith(
        tenantId,
        expect.objectContaining({
          contractCode: 'CT-2023-001',
          employeeId: 1,
          contractType: ContractType.DEFINITE,
          title: 'Employment Contract',
          startDate: new Date('2023-01-15'),
          baseSalary: 10000000,
          status: ContractStatus.DRAFT,
          createdBy: userId,
          updatedBy: userId,
        }),
      );
    });

    it('should throw an exception when contract code already exists', async () => {
      const mockCreateDto: CreateContractDto = {
        contractCode: 'CT-2023-001',
        employeeId: 1,
        contractType: ContractType.DEFINITE,
        title: 'Employment Contract',
        startDate: new Date('2023-01-15'),
        baseSalary: 10000000,
      };
      const existingContract = {
        id: 2,
        contractCode: 'CT-2023-001',
      } as Contract;

      mockContractRepository.findByContractCode.mockResolvedValue(
        existingContract,
      );

      await expect(service.create(mockCreateDto, 1)).rejects.toThrow(
        new AppException(
          HRM_ERROR_CODES.CONTRACT_CODE_EXISTS,
          'Contract with code CT-2023-001 already exists',
        ),
      );
      expect(mockContractRepository.findByContractCode).toHaveBeenCalledWith(
        'CT-2023-001',
      );
      expect(mockContractRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('update', () => {
    it('should update and return the contract', async () => {
      const mockContract = {
        id: 1,
        contractCode: 'CT-2023-001',
        title: 'Employment Contract',
        status: ContractStatus.DRAFT,
      } as Contract;
      const mockUpdateDto: UpdateContractDto = {
        title: 'Updated Employment Contract',
      };
      const mockUpdatedContract = {
        id: 1,
        contractCode: 'CT-2023-001',
        title: 'Updated Employment Contract',
        status: ContractStatus.DRAFT,
      } as Contract;

      mockContractRepository.findById.mockResolvedValue(mockContract);
      mockContractRepository.update.mockResolvedValue(mockUpdatedContract);

      const result = await service.update(1, mockUpdateDto, 1);

      expect(result).toEqual(mockUpdatedContract);
      expect(mockContractRepository.findById).toHaveBeenCalledWith(1);
      expect(mockContractRepository.update).toHaveBeenCalledWith(
        1,
        expect.objectContaining({
          title: 'Updated Employment Contract',
          updatedBy: 1,
        }),
      );
    });

    it('should throw an exception when contract not found', async () => {
      const mockUpdateDto: UpdateContractDto = {
        title: 'Updated Employment Contract',
      };

      mockContractRepository.findById.mockResolvedValue(null);

      await expect(service.update(1, mockUpdateDto, 1)).rejects.toThrow(
        new AppException(
          HRM_ERROR_CODES.CONTRACT_NOT_FOUND,
          'Contract with ID 1 not found',
        ),
      );
      expect(mockContractRepository.findById).toHaveBeenCalledWith(1);
      expect(mockContractRepository.update).not.toHaveBeenCalled();
    });

    it('should throw an exception when trying to update a terminated contract', async () => {
      const mockContract = {
        id: 1,
        contractCode: 'CT-2023-001',
        title: 'Employment Contract',
        status: ContractStatus.TERMINATED,
      } as Contract;
      const mockUpdateDto: UpdateContractDto = {
        title: 'Updated Employment Contract',
      };

      mockContractRepository.findById.mockResolvedValue(mockContract);

      await expect(service.update(1, mockUpdateDto, 1)).rejects.toThrow(
        new AppException(
          HRM_ERROR_CODES.CONTRACT_CANNOT_UPDATE_TERMINATED,
          'Cannot update a terminated contract',
        ),
      );
      expect(mockContractRepository.findById).toHaveBeenCalledWith(1);
      expect(mockContractRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('updateStatus', () => {
    it('should update contract status', async () => {
      const mockContract = {
        id: 1,
        contractCode: 'CT-2023-001',
        status: ContractStatus.DRAFT,
      } as Contract;
      const mockUpdateStatusDto: UpdateContractStatusDto = {
        status: ContractStatus.ACTIVE,
        reason: 'Contract approved',
      };
      const mockUpdatedContract = {
        id: 1,
        contractCode: 'CT-2023-001',
        status: ContractStatus.ACTIVE,
      } as Contract;

      mockContractRepository.findById.mockResolvedValue(mockContract);
      mockContractRepository.update.mockResolvedValue(mockUpdatedContract);

      const result = await service.updateStatus(1, mockUpdateStatusDto, 1);

      expect(result).toEqual(mockUpdatedContract);
      expect(mockContractRepository.findById).toHaveBeenCalledWith(1);
      expect(mockContractRepository.update).toHaveBeenCalledWith(
        1,
        expect.objectContaining({
          status: ContractStatus.ACTIVE,
          updatedBy: 1,
        }),
      );
    });

    it('should throw an exception when trying to change status of a terminated contract', async () => {
      const mockContract = {
        id: 1,
        contractCode: 'CT-2023-001',
        status: ContractStatus.TERMINATED,
      } as Contract;
      const mockUpdateStatusDto: UpdateContractStatusDto = {
        status: ContractStatus.ACTIVE,
      };

      mockContractRepository.findById.mockResolvedValue(mockContract);

      await expect(
        service.updateStatus(1, mockUpdateStatusDto, 1),
      ).rejects.toThrow(
        new AppException(
          HRM_ERROR_CODES.CONTRACT_INVALID_STATUS_CHANGE,
          'Cannot change status of a terminated contract',
        ),
      );
      expect(mockContractRepository.findById).toHaveBeenCalledWith(1);
      expect(mockContractRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('terminateContract', () => {
    it('should terminate a contract', async () => {
      const mockContract = {
        id: 1,
        contractCode: 'CT-2023-001',
        status: ContractStatus.ACTIVE,
      } as Contract;
      const mockTerminateDto: TerminateContractDto = {
        terminationDate: new Date('2023-06-30'),
        terminationReason: 'Employee resigned',
      };
      const mockTerminatedContract = {
        id: 1,
        contractCode: 'CT-2023-001',
        status: ContractStatus.TERMINATED,
        terminationDate: new Date('2023-06-30'),
        terminationReason: 'Employee resigned',
      } as Contract;

      mockContractRepository.findById.mockResolvedValue(mockContract);
      mockContractRepository.update.mockResolvedValue(mockTerminatedContract);

      const result = await service.terminateContract(1, mockTerminateDto, 1);

      expect(result).toEqual(mockTerminatedContract);
      expect(mockContractRepository.findById).toHaveBeenCalledWith(1);
      expect(mockContractRepository.update).toHaveBeenCalledWith(
        1,
        expect.objectContaining({
          status: ContractStatus.TERMINATED,
          terminationDate: new Date('2023-06-30'),
          terminationReason: 'Employee resigned',
          updatedBy: 1,
        }),
      );
    });

    it('should throw an exception when contract is already terminated', async () => {
      const mockContract = {
        id: 1,
        contractCode: 'CT-2023-001',
        status: ContractStatus.TERMINATED,
      } as Contract;
      const mockTerminateDto: TerminateContractDto = {
        terminationDate: new Date('2023-06-30'),
        terminationReason: 'Employee resigned',
      };

      mockContractRepository.findById.mockResolvedValue(mockContract);

      await expect(
        service.terminateContract(1, mockTerminateDto, 1),
      ).rejects.toThrow(
        new AppException(
          HRM_ERROR_CODES.CONTRACT_ALREADY_TERMINATED,
          'Contract is already terminated',
        ),
      );
      expect(mockContractRepository.findById).toHaveBeenCalledWith(1);
      expect(mockContractRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('delete', () => {
    it('should delete a contract and return true', async () => {
      const mockContract = { id: 1, contractCode: 'CT-2023-001' } as Contract;

      mockContractRepository.findById.mockResolvedValue(mockContract);
      mockContractRepository.delete.mockResolvedValue(true);

      const result = await service.delete(1);

      expect(result).toBe(true);
      expect(mockContractRepository.findById).toHaveBeenCalledWith(1);
      expect(mockContractRepository.delete).toHaveBeenCalledWith(1);
    });

    it('should throw an exception when contract not found', async () => {
      mockContractRepository.findById.mockResolvedValue(null);

      await expect(service.delete(1)).rejects.toThrow(
        new AppException(
          HRM_ERROR_CODES.CONTRACT_NOT_FOUND,
          'Contract with ID 1 not found',
        ),
      );
      expect(mockContractRepository.findById).toHaveBeenCalledWith(1);
      expect(mockContractRepository.delete).not.toHaveBeenCalled();
    });
  });
});
