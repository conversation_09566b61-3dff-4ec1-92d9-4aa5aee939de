const { Client } = require('pg');

async function testConnection() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'postgres',
    password: 'postgres',
    database: 'redai_dev'
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected successfully!');
    
    const result = await client.query('SELECT 1 as value');
    console.log('Query result:', result.rows);
    
    await client.end();
    console.log('Connection closed.');
  } catch (error) {
    console.error('Error connecting to database:', error.message);
  }
}

testConnection();
